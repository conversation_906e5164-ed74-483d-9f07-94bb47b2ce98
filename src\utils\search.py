"""
Search-related utilities for TongRAG3

This module contains functions for processing and formatting search results.
"""

from typing import Dict, List


def calculate_relevance_score(distance: float) -> float:
    """
    將 Chroma 距離轉換為 0-100 的相關度分數
    
    Args:
        distance: Chroma 返回的距離值 (0-2之間)
        
    Returns:
        相關度分數 (0-100)
    """
    if distance < 0:
        distance = 0
    elif distance > 2:
        distance = 2
    
    # 將距離轉換為相似度分數 (距離越小，相關度越高)
    similarity = 1.0 - (distance / 2.0)
    return similarity * 100.0


def get_search_stats(results: List[Dict]) -> Dict:
    """
    計算搜索結果統計信息
    
    Args:
        results: 搜索結果列表
        
    Returns:
        統計信息字典
    """
    if not results:
        return {
            "total_results": 0,
            "average_score": 0.0,
            "min_score": 0.0,
            "max_score": 0.0,
            "sources": {},
            "source_count": 0
        }
    
    # 統計相關度分數
    scores = [r.get("relevance_score", 0.0) for r in results]
    avg_score = sum(scores) / len(scores) if scores else 0.0
    
    # 統計來源分布
    sources = {}
    for result in results:
        source = result.get("source", "未知來源")
        sources[source] = sources.get(source, 0) + 1
    
    return {
        "total_results": len(results),
        "average_score": avg_score,
        "min_score": min(scores) if scores else 0.0,
        "max_score": max(scores) if scores else 0.0,
        "sources": sources,
        "source_count": len(sources)
    }


def format_results(results: List[Dict], enable_overlap_detection: bool = True) -> List[Dict]:
    """
    格式化 Chroma 和 search_engine 返回的結果，支持重疊區域檢測
    
    Args:
        results: 原始搜索結果列表
        enable_overlap_detection: 是否啟用重疊檢測
        
    Returns:
        格式化後的結果列表，包含重疊區域分析
    """
    # 使用延遲導入避免循環依賴
    from .text import clean_text, truncate_text_with_count
    
    # 第一階段：基本格式化
    formatted_results = []
    
    for i, result in enumerate(results):
        # 計算相關度分數
        distance = result.get("distance", 0.0)
        relevance_score = calculate_relevance_score(distance)
        
        # 提取和清理文本
        text = result.get("text", "")
        clean_content = clean_text(text)
        
        # 獲取元數據
        metadata = result.get("metadata", {})
        chunk_id = metadata.get("chunk_id", "")
        
        # 生成預覽和剩餘字數
        preview_text, remaining_chars = truncate_text_with_count(clean_content, 150)
        
        # 格式化結果
        formatted_result = {
            "rank": i + 1,
            "id": result.get("id", f"result_{i}"),
            "text": clean_content,
            "preview": preview_text,
            "remaining_chars": remaining_chars,
            "full_text": clean_content,  # 保存完整內容供查看
            "source": result.get("source") or metadata.get("file_name", "未知來源"),
            "metadata": metadata,
            "distance": distance,
            "relevance_score": relevance_score,
            "score_display": f"{relevance_score:.1f}%",
            # 簡化內容類型，統一使用重疊檢測格式
            "content_type": "overlap_format",
            "content_description": "重疊檢測格式",
            "chunk_info": f"塊 {chunk_id}" if chunk_id else "完整內容"
        }
        
        formatted_results.append(formatted_result)
    
    # 第二階段：重疊檢測和增強
    if enable_overlap_detection and len(formatted_results) > 1:
        # 直接導入，如果失敗就報錯，不要靜默處理
        from .overlap_detector import OverlapDetector
        
        print(f"[DEBUG] 開始重疊檢測，處理 {len(formatted_results)} 個結果")
        detector = OverlapDetector()
        
        # 保存原始順序
        for i, result in enumerate(formatted_results):
            result['original_index'] = i
        
        # 執行重疊檢測
        formatted_results = detector.detect_overlaps(formatted_results)
        print(f"[DEBUG] 重疊檢測完成，結果數量: {len(formatted_results)}")
        
        # 恢復原始順序並重新分配排名
        formatted_results.sort(key=lambda x: x.get('original_index', 0))
        for i, result in enumerate(formatted_results):
            result['rank'] = i + 1
            result.pop('original_index', None)  # 清理臨時字段
        
        # 重新格式化包含重疊信息的結果
        enhanced_results = []
        for result in formatted_results:
            enhanced_result = _enhance_result_with_overlap_display(result)
            enhanced_results.append(enhanced_result)
        
        overlap_count = sum(1 for r in enhanced_results if r.get('has_overlap'))
        print(f"[DEBUG] 檢測到 {overlap_count} 個重疊結果")
        
        return enhanced_results
    
    print(f"[DEBUG] 跳過重疊檢測（結果數量: {len(formatted_results)}, 啟用檢測: {enable_overlap_detection}）")
    return formatted_results


def _enhance_result_with_overlap_display(result: Dict) -> Dict:
    """
    增強結果以支持重疊區域顯示
    
    Args:
        result: 包含重疊檢測信息的結果
        
    Returns:
        增強的結果，包含分區顯示信息
    """
    # 使用延遲導入避免循環依賴
    from .text import truncate_text_with_count
    
    enhanced = result.copy()
    
    # 添加詳細的調試日誌
    chunk_id = result.get('metadata', {}).get('chunk_id', '?')
    has_overlap = result.get('has_overlap', False)
    overlap_info = result.get('overlap_info', {})
    overlap_content = overlap_info.get('overlap_content', '')
    unique_content = overlap_info.get('unique_content', '')
    
    print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: has_overlap={has_overlap}")
    print(f"[_enhance_result_with_overlap_display] overlap_content 長度: {len(overlap_content)}")
    print(f"[_enhance_result_with_overlap_display] unique_content 長度: {len(unique_content)}")
    
    # 強制使用新格式，不再檢查 has_overlap
    # 即使沒有重疊也要使用重疊格式顯示
    
    # 為重疊區域生成預覽
    if overlap_content:
        overlap_preview, overlap_remaining = truncate_text_with_count(overlap_content, 100)
        enhanced['overlap_preview'] = overlap_preview
        enhanced['overlap_remaining_chars'] = overlap_remaining
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了 overlap_preview")
    else:
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: overlap_content 為空，設為空字符串")
        enhanced['overlap_preview'] = ''
        enhanced['overlap_remaining_chars'] = 0
    
    # 為獨有區域生成預覽 
    if unique_content:
        unique_preview, unique_remaining = truncate_text_with_count(unique_content, 150)
        enhanced['unique_preview'] = unique_preview
        enhanced['unique_remaining_chars'] = unique_remaining
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了 unique_preview")
    else:
        # 沒有 unique_content 時，使用原始內容作為獨有區域
        original_content = result.get('document', '')
        if original_content:
            unique_preview, unique_remaining = truncate_text_with_count(original_content, 150)
            enhanced['unique_preview'] = unique_preview
            enhanced['unique_remaining_chars'] = unique_remaining
            print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: unique_content 為空，使用原始內容")
        else:
            enhanced['unique_preview'] = ''
            enhanced['unique_remaining_chars'] = 0
            print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 所有內容都為空！")
    
    # 強制使用重疊格式 - 移除條件檢查
    enhanced['content_type'] = 'overlap_detected'
    enhanced['content_description'] = '重疊檢測格式'
    print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 強制設置 content_type = 'overlap_detected'")
    
    # 創建重疊來源信息
    overlap_sources = overlap_info.get('overlap_sources', [])
    if overlap_sources:
        source_info = []
        for source in overlap_sources[:2]:  # 最多顯示2個來源
            chunk_id_src = source.get('chunk_id', '')
            start_char = source.get('start_char', 0)
            end_char = source.get('end_char', 0)
            source_info.append(f"塊 {chunk_id_src} ({start_char}-{end_char})")
        enhanced['overlap_source_info'] = ', '.join(source_info)
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了重疊來源信息")
    else:
        enhanced['overlap_source_info'] = ''
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 無重疊來源信息")
    
    return enhanced