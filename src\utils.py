"""
Utility Functions for TongRAG3

This module contains common utility functions and helpers
used throughout the application.
"""

import json
import csv
import re
import string
from datetime import datetime
from pathlib import Path
from typing import Dict, List

try:
    from colorama import Fore, Style, init
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    # 如果沒有 colorama，定義空的顏色常量
    class _Color:
        def __getattr__(self, name): return ""
    Fore = Style = _Color()




def convert_english_to_uppercase(text: str) -> str:
    """
    將字符串中的英文字母轉換為大寫，保持中文和其他字符不變。
    
    Args:
        text: 輸入文字串
        
    Returns:
        轉換後的文字串（英文字母大寫，其他字符保持原樣）
        
    Examples:
        >>> convert_english_to_uppercase("Hello 世界 API test")
        "HELLO 世界 API TEST"
        >>> convert_english_to_uppercase("測試 cpu usage")
        "測試 CPU USAGE"
    """
    if not text:
        return text
    
    result = []
    for char in text:
        # 只對英文字母進行大寫轉換
        if char in string.ascii_letters:
            result.append(char.upper())
        else:
            result.append(char)
    
    return ''.join(result)


def format_results(results: List[Dict], enable_overlap_detection: bool = True) -> List[Dict]:
    """
    格式化 Chroma 和 search_engine 返回的結果，支持重疊區域檢測
    
    Args:
        results: 原始搜索結果列表
        enable_overlap_detection: 是否啟用重疊檢測
        
    Returns:
        格式化後的結果列表，包含重疊區域分析
    """
    # 第一階段：基本格式化
    formatted_results = []
    
    for i, result in enumerate(results):
        # 計算相關度分數
        distance = result.get("distance", 0.0)
        relevance_score = calculate_relevance_score(distance)
        
        # 提取和清理文本
        text = result.get("text", "")
        clean_content = clean_text(text)
        
        # 獲取元數據
        metadata = result.get("metadata", {})
        chunk_id = metadata.get("chunk_id", "")
        
        # 生成預覽和剩餘字數
        preview_text, remaining_chars = truncate_text_with_count(clean_content, 150)
        
        # 格式化結果
        formatted_result = {
            "rank": i + 1,
            "id": result.get("id", f"result_{i}"),
            "text": clean_content,
            "preview": preview_text,
            "remaining_chars": remaining_chars,
            "full_text": clean_content,  # 保存完整內容供查看
            "source": result.get("source") or metadata.get("file_name", "未知來源"),
            "metadata": metadata,
            "distance": distance,
            "relevance_score": relevance_score,
            "score_display": f"{relevance_score:.1f}%",
            # 簡化內容類型，統一使用重疊檢測格式
            "content_type": "overlap_format",
            "content_description": "重疊檢測格式",
            "chunk_info": f"塊 {chunk_id}" if chunk_id else "完整內容"
        }
        
        formatted_results.append(formatted_result)
    
    # 第二階段：重疊檢測和增強
    if enable_overlap_detection and len(formatted_results) > 1:
        # 直接導入，如果失敗就報錯，不要靜默處理
        from src.utils.overlap_detector import OverlapDetector
        
        print(f"[DEBUG] 開始重疊檢測，處理 {len(formatted_results)} 個結果")
        detector = OverlapDetector()
        
        # 保存原始順序
        for i, result in enumerate(formatted_results):
            result['original_index'] = i
        
        # 執行重疊檢測
        formatted_results = detector.detect_overlaps(formatted_results)
        print(f"[DEBUG] 重疊檢測完成，結果數量: {len(formatted_results)}")
        
        # 恢復原始順序並重新分配排名
        formatted_results.sort(key=lambda x: x.get('original_index', 0))
        for i, result in enumerate(formatted_results):
            result['rank'] = i + 1
            result.pop('original_index', None)  # 清理臨時字段
        
        # 重新格式化包含重疊信息的結果
        enhanced_results = []
        for result in formatted_results:
            enhanced_result = _enhance_result_with_overlap_display(result)
            enhanced_results.append(enhanced_result)
        
        overlap_count = sum(1 for r in enhanced_results if r.get('has_overlap'))
        print(f"[DEBUG] 檢測到 {overlap_count} 個重疊結果")
        
        return enhanced_results
    
    print(f"[DEBUG] 跳過重疊檢測（結果數量: {len(formatted_results)}, 啟用檢測: {enable_overlap_detection}）")
    return formatted_results




def calculate_relevance_score(distance: float) -> float:
    """
    將 Chroma 距離轉換為 0-100 的相關度分數
    
    Args:
        distance: Chroma 返回的距離值 (0-2之間)
        
    Returns:
        相關度分數 (0-100)
    """
    if distance < 0:
        distance = 0
    elif distance > 2:
        distance = 2
    
    # 將距離轉換為相似度分數 (距離越小，相關度越高)
    similarity = 1.0 - (distance / 2.0)
    return similarity * 100.0




def validate_query(query: str) -> bool:
    """
    驗證查詢字符串的有效性
    
    Args:
        query: 查詢字符串
        
    Returns:
        是否有效
    """
    if not query or not isinstance(query, str):
        return False
    
    # 清理查詢
    clean_query = query.strip()
    
    # 檢查長度
    if len(clean_query) == 0 or len(clean_query) > 1000:
        return False
    
    # 檢查是否只包含空白字符
    if not clean_query or clean_query.isspace():
        return False
    
    # 檢查惡意模式
    malicious_patterns = [
        r'<script.*?>',  # XSS
        r'javascript:',   # JavaScript injection
        r'eval\(',       # Code execution
        r'exec\(',       # Code execution
        r'__import__',   # Python import injection
    ]
    
    for pattern in malicious_patterns:
        if re.search(pattern, clean_query, re.IGNORECASE):
            return False
    
    return True


def export_results(results: List[Dict], format: str = "json", filename: str = None) -> str:
    """
    導出搜索結果到文件
    
    Args:
        results: 搜索結果列表
        format: 導出格式 (json, csv, txt)
        filename: 文件名（如果為None則自動生成）
        
    Returns:
        導出的文件路径
    """
    if not results:
        raise ValueError("沒有結果需要導出")
    
    # 生成文件名
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"search_results_{timestamp}.{format}"
    
    # 確保文件擴展名正確
    if not filename.endswith(f'.{format}'):
        filename = f"{filename}.{format}"
    
    file_path = Path(filename).absolute()
    
    try:
        if format.lower() == "json":
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == "csv":
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                if results:
                    writer = csv.DictWriter(f, fieldnames=results[0].keys())
                    writer.writeheader()
                    for result in results:
                        # 將複雜對象轉換為字符串
                        row = {}
                        for k, v in result.items():
                            if isinstance(v, (dict, list)):
                                row[k] = json.dumps(v, ensure_ascii=False)
                            else:
                                row[k] = str(v)
                        writer.writerow(row)
        
        elif format.lower() == "txt":
            with open(file_path, 'w', encoding='utf-8') as f:
                for i, result in enumerate(results, 1):
                    f.write(f"===== 結果 {i} =====\n")
                    f.write(f"ID: {result.get('id', 'N/A')}\n")
                    f.write(f"來源: {result.get('source', 'N/A')}\n")
                    f.write(f"相關度: {result.get('score_display', 'N/A')}\n")
                    f.write(f"內容:\n{result.get('text', 'N/A')}\n\n")
        
        else:
            raise ValueError(f"不支持的導出格式: {format}")
        
        return str(file_path)
        
    except Exception as e:
        raise IOError(f"導出文件失敗: {str(e)}")


def clean_text(text: str) -> str:
    """
    清理和標準化文本
    
    Args:
        text: 原始文本
        
    Returns:
        清理後的文本
    """
    if not text:
        return ""
    
    # 統一換行符
    cleaned = text.replace('\r\n', '\n').replace('\r', '\n')
    
    # 移除行首行尾空白
    lines = [line.strip() for line in cleaned.split('\n')]
    
    # 過濾空行，但保留一些結構
    filtered_lines = []
    prev_empty = False
    
    for line in lines:
        if line:  # 非空行
            filtered_lines.append(line)
            prev_empty = False
        else:  # 空行
            if not prev_empty:  # 避免連續空行
                filtered_lines.append("")
            prev_empty = True
    
    # 重新組合並清理多餘空白
    cleaned = '\n'.join(filtered_lines)
    
    # 在每行內部清理多餘空白
    lines = cleaned.split('\n')
    cleaned_lines = []
    for line in lines:
        if line.strip():  # 非空行
            # 將多個空白字符替換為單個空格
            cleaned_line = re.sub(r'\s+', ' ', line.strip())
            cleaned_lines.append(cleaned_line)
        else:
            cleaned_lines.append("")  # 保留空行
    
    return '\n'.join(cleaned_lines).strip()


def truncate_text_with_count(text: str, max_length: int = 200) -> tuple:
    """
    截斷文本並返回剩餘字數
    
    Args:
        text: 要截斷的文本
        max_length: 最大長度
        
    Returns:
        (截斷後的文本, 剩餘字數)
    """
    if not text or len(text) <= max_length:
        return text, 0
    
    # 使用現有的截斷邏輯
    truncated = truncate_text(text, max_length)
    
    # 計算實際剩餘字數（排除"..."）
    truncated_clean = truncated.rstrip('...')
    remaining_chars = len(text) - len(truncated_clean)
    
    return truncated, max(0, remaining_chars)


def truncate_text(text: str, max_length: int = 200) -> str:
    """
    智能截斷文本，保持完整句子
    
    Args:
        text: 要截斷的文本
        max_length: 最大長度
        
    Returns:
        截斷後的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    # 如果文本太短，直接截斷
    if max_length < 10:
        return text[:max_length] + "..."
    
    # 嘗試在句子邊界截斷
    sentence_endings = ['. ', '。', '！', '？', '!', '?', '\n']
    
    # 在最大長度附近查找句子結束符
    search_start = max(0, max_length - 50)
    search_end = min(len(text), max_length)
    
    best_cut = -1
    for i in range(search_end - 1, search_start, -1):
        for ending in sentence_endings:
            if text[i:i+len(ending)] == ending:
                best_cut = i + len(ending)
                break
        if best_cut != -1:
            break
    
    # 如果找到合適的截斷點
    if best_cut != -1 and best_cut < max_length:
        return text[:best_cut].strip()
    
    # 否則在單詞邊界截斷
    truncated = text[:max_length - 3]
    last_space = truncated.rfind(' ')
    if last_space > max_length * 0.8:  # 如果空格位置合理
        truncated = truncated[:last_space]
    
    return truncated.strip() + "..."


def get_search_stats(results: List[Dict]) -> Dict:
    """
    計算搜索結果統計信息
    
    Args:
        results: 搜索結果列表
        
    Returns:
        統計信息字典
    """
    if not results:
        return {
            "total_results": 0,
            "average_score": 0.0,
            "min_score": 0.0,
            "max_score": 0.0,
            "sources": {},
            "source_count": 0
        }
    
    # 統計相關度分數
    scores = [r.get("relevance_score", 0.0) for r in results]
    avg_score = sum(scores) / len(scores) if scores else 0.0
    
    # 統計來源分布
    sources = {}
    for result in results:
        source = result.get("source", "未知來源")
        sources[source] = sources.get(source, 0) + 1
    
    return {
        "total_results": len(results),
        "average_score": avg_score,
        "min_score": min(scores) if scores else 0.0,
        "max_score": max(scores) if scores else 0.0,
        "sources": sources,
        "source_count": len(sources)
    }




def _enhance_result_with_overlap_display(result: Dict) -> Dict:
    """
    增強結果以支持重疊區域顯示
    
    Args:
        result: 包含重疊檢測信息的結果
        
    Returns:
        增強的結果，包含分區顯示信息
    """
    enhanced = result.copy()
    
    # 添加詳細的調試日誌
    chunk_id = result.get('metadata', {}).get('chunk_id', '?')
    has_overlap = result.get('has_overlap', False)
    overlap_info = result.get('overlap_info', {})
    overlap_content = overlap_info.get('overlap_content', '')
    unique_content = overlap_info.get('unique_content', '')
    
    print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: has_overlap={has_overlap}")
    print(f"[_enhance_result_with_overlap_display] overlap_content 長度: {len(overlap_content)}")
    print(f"[_enhance_result_with_overlap_display] unique_content 長度: {len(unique_content)}")
    
    # 強制使用新格式，不再檢查 has_overlap
    # 即使沒有重疊也要使用重疊格式顯示
    
    # 為重疊區域生成預覽
    if overlap_content:
        overlap_preview, overlap_remaining = truncate_text_with_count(overlap_content, 100)
        enhanced['overlap_preview'] = overlap_preview
        enhanced['overlap_remaining_chars'] = overlap_remaining
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了 overlap_preview")
    else:
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: overlap_content 為空，設為空字符串")
        enhanced['overlap_preview'] = ''
        enhanced['overlap_remaining_chars'] = 0
    
    # 為獨有區域生成預覽 
    if unique_content:
        unique_preview, unique_remaining = truncate_text_with_count(unique_content, 150)
        enhanced['unique_preview'] = unique_preview
        enhanced['unique_remaining_chars'] = unique_remaining
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了 unique_preview")
    else:
        # 沒有 unique_content 時，使用原始內容作為獨有區域
        original_content = result.get('document', '')
        if original_content:
            unique_preview, unique_remaining = truncate_text_with_count(original_content, 150)
            enhanced['unique_preview'] = unique_preview
            enhanced['unique_remaining_chars'] = unique_remaining
            print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: unique_content 為空，使用原始內容")
        else:
            enhanced['unique_preview'] = ''
            enhanced['unique_remaining_chars'] = 0
            print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 所有內容都為空！")
    
    # 強制使用重疊格式 - 移除條件檢查
    enhanced['content_type'] = 'overlap_detected'
    enhanced['content_description'] = '重疊檢測格式'
    print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 強制設置 content_type = 'overlap_detected'")
    
    # 創建重疊來源信息
    overlap_sources = overlap_info.get('overlap_sources', [])
    if overlap_sources:
        source_info = []
        for source in overlap_sources[:2]:  # 最多顯示2個來源
            chunk_id_src = source.get('chunk_id', '')
            start_char = source.get('start_char', 0)
            end_char = source.get('end_char', 0)
            source_info.append(f"塊 {chunk_id_src} ({start_char}-{end_char})")
        enhanced['overlap_source_info'] = ', '.join(source_info)
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 設置了重疊來源信息")
    else:
        enhanced['overlap_source_info'] = ''
        print(f"[_enhance_result_with_overlap_display] 塊 {chunk_id}: 無重疊來源信息")
    
    return enhanced


